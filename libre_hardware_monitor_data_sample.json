{"id": 0, "Text": "Sensor", "Min": "Min", "Value": "Value", "Max": "Max", "ImageURL": "", "Children": [{"id": 1, "Text": "WINDOWS-GAME", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/computer.png", "Children": [{"id": 2, "Text": "BATTLE-AX B760M-T PRO", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/mainboard.png", "Children": [{"id": 3, "Text": "Nuvoton NCT6796D-R", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/chip.png", "Children": [{"id": 4, "Text": "Voltages", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/voltage.png", "Children": [{"id": 5, "Text": "Vcore", "Min": "0.784 V", "Value": "0.880 V", "Max": "1.056 V", "SensorId": "/lpc/nct6796dr/0/voltage/0", "Type": "Voltage", "ImageURL": "images/transparent.png", "Children": []}, {"id": 6, "Text": "Voltage #2", "Min": "1.000 V", "Value": "1.000 V", "Max": "1.008 V", "SensorId": "/lpc/nct6796dr/0/voltage/1", "Type": "Voltage", "ImageURL": "images/transparent.png", "Children": []}, {"id": 7, "Text": "AVCC", "Min": "3.344 V", "Value": "3.344 V", "Max": "3.344 V", "SensorId": "/lpc/nct6796dr/0/voltage/2", "Type": "Voltage", "ImageURL": "images/transparent.png", "Children": []}, {"id": 8, "Text": "+3.3V", "Min": "3.296 V", "Value": "3.296 V", "Max": "3.312 V", "SensorId": "/lpc/nct6796dr/0/voltage/3", "Type": "Voltage", "ImageURL": "images/transparent.png", "Children": []}, {"id": 9, "Text": "Voltage #5", "Min": "1.008 V", "Value": "1.016 V", "Max": "1.016 V", "SensorId": "/lpc/nct6796dr/0/voltage/4", "Type": "Voltage", "ImageURL": "images/transparent.png", "Children": []}, {"id": 10, "Text": "Voltage #6", "Min": "0.112 V", "Value": "0.112 V", "Max": "0.112 V", "SensorId": "/lpc/nct6796dr/0/voltage/5", "Type": "Voltage", "ImageURL": "images/transparent.png", "Children": []}, {"id": 11, "Text": "Voltage #7", "Min": "0.104 V", "Value": "0.104 V", "Max": "0.104 V", "SensorId": "/lpc/nct6796dr/0/voltage/6", "Type": "Voltage", "ImageURL": "images/transparent.png", "Children": []}, {"id": 12, "Text": "+3V Standby", "Min": "3.328 V", "Value": "3.344 V", "Max": "3.344 V", "SensorId": "/lpc/nct6796dr/0/voltage/7", "Type": "Voltage", "ImageURL": "images/transparent.png", "Children": []}, {"id": 13, "Text": "CMOS Battery", "Min": "3.104 V", "Value": "3.104 V", "Max": "3.104 V", "SensorId": "/lpc/nct6796dr/0/voltage/8", "Type": "Voltage", "ImageURL": "images/transparent.png", "Children": []}, {"id": 14, "Text": "CPU Termination", "Min": "0.528 V", "Value": "0.536 V", "Max": "0.536 V", "SensorId": "/lpc/nct6796dr/0/voltage/9", "Type": "Voltage", "ImageURL": "images/transparent.png", "Children": []}, {"id": 15, "Text": "Voltage #11", "Min": "0.064 V", "Value": "0.072 V", "Max": "0.072 V", "SensorId": "/lpc/nct6796dr/0/voltage/10", "Type": "Voltage", "ImageURL": "images/transparent.png", "Children": []}, {"id": 16, "Text": "Voltage #12", "Min": "0.048 V", "Value": "0.048 V", "Max": "0.048 V", "SensorId": "/lpc/nct6796dr/0/voltage/11", "Type": "Voltage", "ImageURL": "images/transparent.png", "Children": []}, {"id": 17, "Text": "Voltage #13", "Min": "1.216 V", "Value": "1.216 V", "Max": "1.216 V", "SensorId": "/lpc/nct6796dr/0/voltage/12", "Type": "Voltage", "ImageURL": "images/transparent.png", "Children": []}, {"id": 18, "Text": "Voltage #14", "Min": "0.136 V", "Value": "0.144 V", "Max": "0.144 V", "SensorId": "/lpc/nct6796dr/0/voltage/13", "Type": "Voltage", "ImageURL": "images/transparent.png", "Children": []}, {"id": 19, "Text": "Voltage #15", "Min": "1.256 V", "Value": "1.256 V", "Max": "1.256 V", "SensorId": "/lpc/nct6796dr/0/voltage/14", "Type": "Voltage", "ImageURL": "images/transparent.png", "Children": []}]}, {"id": 20, "Text": "Temperatures", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/temperature.png", "Children": [{"id": 21, "Text": "CPU Core", "Min": "32.0 °C", "Value": "36.0 °C", "Max": "42.5 °C", "SensorId": "/lpc/nct6796dr/0/temperature/0", "Type": "Temperature", "ImageURL": "images/transparent.png", "Children": []}, {"id": 22, "Text": "Temperature #1", "Min": "30.0 °C", "Value": "36.0 °C", "Max": "36.0 °C", "SensorId": "/lpc/nct6796dr/0/temperature/1", "Type": "Temperature", "ImageURL": "images/transparent.png", "Children": []}, {"id": 23, "Text": "Temperature #2", "Min": "124.0 °C", "Value": "124.0 °C", "Max": "125.0 °C", "SensorId": "/lpc/nct6796dr/0/temperature/2", "Type": "Temperature", "ImageURL": "images/transparent.png", "Children": []}, {"id": 24, "Text": "Temperature #3", "Min": "115.0 °C", "Value": "115.0 °C", "Max": "115.5 °C", "SensorId": "/lpc/nct6796dr/0/temperature/3", "Type": "Temperature", "ImageURL": "images/transparent.png", "Children": []}, {"id": 25, "Text": "Temperature #4", "Min": "123.0 °C", "Value": "124.0 °C", "Max": "125.0 °C", "SensorId": "/lpc/nct6796dr/0/temperature/4", "Type": "Temperature", "ImageURL": "images/transparent.png", "Children": []}, {"id": 26, "Text": "Temperature #5", "Min": "123.0 °C", "Value": "123.0 °C", "Max": "125.0 °C", "SensorId": "/lpc/nct6796dr/0/temperature/5", "Type": "Temperature", "ImageURL": "images/transparent.png", "Children": []}, {"id": 27, "Text": "Temperature #6", "Min": "13.0 °C", "Value": "13.0 °C", "Max": "13.0 °C", "SensorId": "/lpc/nct6796dr/0/temperature/6", "Type": "Temperature", "ImageURL": "images/transparent.png", "Children": []}]}, {"id": 28, "Text": "Fans", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/fan.png", "Children": [{"id": 29, "Text": "Fan #1", "Min": "380 RPM", "Value": "383 RPM", "Max": "740 RPM", "SensorId": "/lpc/nct6796dr/0/fan/0", "Type": "Fan", "ImageURL": "images/transparent.png", "Children": []}, {"id": 30, "Text": "Fan #2", "Min": "422 RPM", "Value": "459 RPM", "Max": "823 RPM", "SensorId": "/lpc/nct6796dr/0/fan/1", "Type": "Fan", "ImageURL": "images/transparent.png", "Children": []}, {"id": 31, "Text": "Fan #3", "Min": "0 RPM", "Value": "0 RPM", "Max": "0 RPM", "SensorId": "/lpc/nct6796dr/0/fan/2", "Type": "Fan", "ImageURL": "images/transparent.png", "Children": []}, {"id": 32, "Text": "Fan #4", "Min": "0 RPM", "Value": "0 RPM", "Max": "0 RPM", "SensorId": "/lpc/nct6796dr/0/fan/3", "Type": "Fan", "ImageURL": "images/transparent.png", "Children": []}, {"id": 33, "Text": "Fan #5", "Min": "0 RPM", "Value": "0 RPM", "Max": "0 RPM", "SensorId": "/lpc/nct6796dr/0/fan/4", "Type": "Fan", "ImageURL": "images/transparent.png", "Children": []}, {"id": 34, "Text": "Fan #6", "Min": "391 RPM", "Value": "429 RPM", "Max": "767 RPM", "SensorId": "/lpc/nct6796dr/0/fan/5", "Type": "Fan", "ImageURL": "images/transparent.png", "Children": []}, {"id": 35, "Text": "Fan #7", "Min": "495 RPM", "Value": "497 RPM", "Max": "1598 RPM", "SensorId": "/lpc/nct6796dr/0/fan/6", "Type": "Fan", "ImageURL": "images/transparent.png", "Children": []}]}, {"id": 36, "Text": "Controls", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/control.png", "Children": [{"id": 37, "Text": "Fan #1", "Min": "0.0 %", "Value": "0.0 %", "Max": "0.0 %", "SensorId": "/lpc/nct6796dr/0/control/0", "Type": "Control", "ImageURL": "images/transparent.png", "Children": []}, {"id": 38, "Text": "Fan #2", "Min": "20.0 %", "Value": "22.0 %", "Max": "22.7 %", "SensorId": "/lpc/nct6796dr/0/control/1", "Type": "Control", "ImageURL": "images/transparent.png", "Children": []}, {"id": 39, "Text": "Fan #3", "Min": "60.0 %", "Value": "60.0 %", "Max": "60.0 %", "SensorId": "/lpc/nct6796dr/0/control/2", "Type": "Control", "ImageURL": "images/transparent.png", "Children": []}, {"id": 40, "Text": "Fan #4", "Min": "60.0 %", "Value": "60.0 %", "Max": "60.0 %", "SensorId": "/lpc/nct6796dr/0/control/3", "Type": "Control", "ImageURL": "images/transparent.png", "Children": []}, {"id": 41, "Text": "Fan #5", "Min": "60.0 %", "Value": "60.0 %", "Max": "60.0 %", "SensorId": "/lpc/nct6796dr/0/control/4", "Type": "Control", "ImageURL": "images/transparent.png", "Children": []}, {"id": 42, "Text": "Fan #6", "Min": "0.0 %", "Value": "0.0 %", "Max": "0.0 %", "SensorId": "/lpc/nct6796dr/0/control/5", "Type": "Control", "ImageURL": "images/transparent.png", "Children": []}, {"id": 43, "Text": "Fan #7", "Min": "0.0 %", "Value": "0.0 %", "Max": "0.0 %", "SensorId": "/lpc/nct6796dr/0/control/6", "Type": "Control", "ImageURL": "images/transparent.png", "Children": []}]}]}]}, {"id": 44, "Text": "12th Gen Intel Core i5-12490F", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/cpu.png", "Children": [{"id": 45, "Text": "Voltages", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/voltage.png", "Children": [{"id": 46, "Text": "CPU Core", "Min": "0.772 V", "Value": "1.027 V", "Max": "1.059 V", "SensorId": "/intelcpu/0/voltage/0", "Type": "Voltage", "ImageURL": "images/transparent.png", "Children": []}, {"id": 47, "Text": "CPU Core #1", "Min": "0.767 V", "Value": "1.027 V", "Max": "1.058 V", "SensorId": "/intelcpu/0/voltage/1", "Type": "Voltage", "ImageURL": "images/transparent.png", "Children": []}, {"id": 48, "Text": "CPU Core #2", "Min": "0.766 V", "Value": "1.012 V", "Max": "1.059 V", "SensorId": "/intelcpu/0/voltage/2", "Type": "Voltage", "ImageURL": "images/transparent.png", "Children": []}, {"id": 49, "Text": "CPU Core #3", "Min": "0.767 V", "Value": "1.002 V", "Max": "1.058 V", "SensorId": "/intelcpu/0/voltage/3", "Type": "Voltage", "ImageURL": "images/transparent.png", "Children": []}, {"id": 50, "Text": "CPU Core #4", "Min": "0.762 V", "Value": "1.012 V", "Max": "1.055 V", "SensorId": "/intelcpu/0/voltage/4", "Type": "Voltage", "ImageURL": "images/transparent.png", "Children": []}, {"id": 51, "Text": "CPU Core #5", "Min": "0.767 V", "Value": "1.002 V", "Max": "1.055 V", "SensorId": "/intelcpu/0/voltage/5", "Type": "Voltage", "ImageURL": "images/transparent.png", "Children": []}, {"id": 52, "Text": "CPU Core #6", "Min": "0.767 V", "Value": "1.010 V", "Max": "1.052 V", "SensorId": "/intelcpu/0/voltage/6", "Type": "Voltage", "ImageURL": "images/transparent.png", "Children": []}]}, {"id": 53, "Text": "Powers", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/power.png", "Children": [{"id": 54, "Text": "CPU Package", "Min": "7.5 W", "Value": "16.5 W", "Max": "47.7 W", "SensorId": "/intelcpu/0/power/0", "Type": "Power", "ImageURL": "images/transparent.png", "Children": []}, {"id": 55, "Text": "CPU Cores", "Min": "5.7 W", "Value": "14.3 W", "Max": "45.4 W", "SensorId": "/intelcpu/0/power/1", "Type": "Power", "ImageURL": "images/transparent.png", "Children": []}, {"id": 56, "Text": "CPU Memory", "Min": "0.0 W", "Value": "0.0 W", "Max": "0.0 W", "SensorId": "/intelcpu/0/power/3", "Type": "Power", "ImageURL": "images/transparent.png", "Children": []}, {"id": 57, "Text": "CPU Platform", "Min": "0.0 W", "Value": "0.0 W", "Max": "0.0 W", "SensorId": "/intelcpu/0/power/4", "Type": "Power", "ImageURL": "images/transparent.png", "Children": []}]}, {"id": 58, "Text": "Clocks", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/clock.png", "Children": [{"id": 59, "Text": "Bus Speed", "Min": "99.8 MHz", "Value": "99.8 MHz", "Max": "99.8 MHz", "SensorId": "/intelcpu/0/clock/0", "Type": "Clock", "ImageURL": "images/transparent.png", "Children": []}, {"id": 60, "Text": "CPU Core #1", "Min": "1797.1 MHz", "Value": "3793.9 MHz", "Max": "3993.6 MHz", "SensorId": "/intelcpu/0/clock/1", "Type": "Clock", "ImageURL": "images/transparent.png", "Children": []}, {"id": 61, "Text": "CPU Core #2", "Min": "1797.1 MHz", "Value": "3893.8 MHz", "Max": "3993.6 MHz", "SensorId": "/intelcpu/0/clock/2", "Type": "Clock", "ImageURL": "images/transparent.png", "Children": []}, {"id": 62, "Text": "CPU Core #3", "Min": "1797.1 MHz", "Value": "3394.6 MHz", "Max": "3993.6 MHz", "SensorId": "/intelcpu/0/clock/3", "Type": "Clock", "ImageURL": "images/transparent.png", "Children": []}, {"id": 63, "Text": "CPU Core #4", "Min": "798.7 MHz", "Value": "3793.9 MHz", "Max": "3993.6 MHz", "SensorId": "/intelcpu/0/clock/4", "Type": "Clock", "ImageURL": "images/transparent.png", "Children": []}, {"id": 64, "Text": "CPU Core #5", "Min": "1797.1 MHz", "Value": "3594.2 MHz", "Max": "3993.6 MHz", "SensorId": "/intelcpu/0/clock/5", "Type": "Clock", "ImageURL": "images/transparent.png", "Children": []}, {"id": 65, "Text": "CPU Core #6", "Min": "1697.3 MHz", "Value": "3793.9 MHz", "Max": "3993.6 MHz", "SensorId": "/intelcpu/0/clock/6", "Type": "Clock", "ImageURL": "images/transparent.png", "Children": []}]}, {"id": 66, "Text": "Temperatures", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/temperature.png", "Children": [{"id": 67, "Text": "Core Max", "Min": "29.0 °C", "Value": "35.0 °C", "Max": "42.0 °C", "SensorId": "/intelcpu/0/temperature/0", "Type": "Temperature", "ImageURL": "images/transparent.png", "Children": []}, {"id": 68, "Text": "Core Average", "Min": "28.0 °C", "Value": "32.2 °C", "Max": "38.7 °C", "SensorId": "/intelcpu/0/temperature/1", "Type": "Temperature", "ImageURL": "images/transparent.png", "Children": []}, {"id": 69, "Text": "CPU Core #1", "Min": "29.0 °C", "Value": "33.0 °C", "Max": "42.0 °C", "SensorId": "/intelcpu/0/temperature/2", "Type": "Temperature", "ImageURL": "images/transparent.png", "Children": []}, {"id": 70, "Text": "CPU Core #2", "Min": "28.0 °C", "Value": "35.0 °C", "Max": "40.0 °C", "SensorId": "/intelcpu/0/temperature/3", "Type": "Temperature", "ImageURL": "images/transparent.png", "Children": []}, {"id": 71, "Text": "CPU Core #3", "Min": "25.0 °C", "Value": "29.0 °C", "Max": "37.0 °C", "SensorId": "/intelcpu/0/temperature/4", "Type": "Temperature", "ImageURL": "images/transparent.png", "Children": []}, {"id": 72, "Text": "CPU Core #4", "Min": "27.0 °C", "Value": "31.0 °C", "Max": "38.0 °C", "SensorId": "/intelcpu/0/temperature/5", "Type": "Temperature", "ImageURL": "images/transparent.png", "Children": []}, {"id": 73, "Text": "CPU Core #5", "Min": "28.0 °C", "Value": "33.0 °C", "Max": "39.0 °C", "SensorId": "/intelcpu/0/temperature/6", "Type": "Temperature", "ImageURL": "images/transparent.png", "Children": []}, {"id": 74, "Text": "CPU Core #6", "Min": "26.0 °C", "Value": "32.0 °C", "Max": "41.0 °C", "SensorId": "/intelcpu/0/temperature/7", "Type": "Temperature", "ImageURL": "images/transparent.png", "Children": []}, {"id": 75, "Text": "CPU Package", "Min": "33.0 °C", "Value": "35.0 °C", "Max": "40.0 °C", "SensorId": "/intelcpu/0/temperature/8", "Type": "Temperature", "ImageURL": "images/transparent.png", "Children": []}, {"id": 76, "Text": "CPU Core #1 Distance to TjMax", "Min": "58.0 °C", "Value": "67.0 °C", "Max": "71.0 °C", "SensorId": "/intelcpu/0/temperature/9", "Type": "Temperature", "ImageURL": "images/transparent.png", "Children": []}, {"id": 77, "Text": "CPU Core #2 Distance to TjMax", "Min": "60.0 °C", "Value": "65.0 °C", "Max": "72.0 °C", "SensorId": "/intelcpu/0/temperature/10", "Type": "Temperature", "ImageURL": "images/transparent.png", "Children": []}, {"id": 78, "Text": "CPU Core #3 Distance to TjMax", "Min": "63.0 °C", "Value": "71.0 °C", "Max": "75.0 °C", "SensorId": "/intelcpu/0/temperature/11", "Type": "Temperature", "ImageURL": "images/transparent.png", "Children": []}, {"id": 79, "Text": "CPU Core #4 Distance to TjMax", "Min": "62.0 °C", "Value": "69.0 °C", "Max": "73.0 °C", "SensorId": "/intelcpu/0/temperature/12", "Type": "Temperature", "ImageURL": "images/transparent.png", "Children": []}, {"id": 80, "Text": "CPU Core #5 Distance to TjMax", "Min": "61.0 °C", "Value": "67.0 °C", "Max": "72.0 °C", "SensorId": "/intelcpu/0/temperature/13", "Type": "Temperature", "ImageURL": "images/transparent.png", "Children": []}, {"id": 81, "Text": "CPU Core #6 Distance to TjMax", "Min": "59.0 °C", "Value": "68.0 °C", "Max": "74.0 °C", "SensorId": "/intelcpu/0/temperature/14", "Type": "Temperature", "ImageURL": "images/transparent.png", "Children": []}]}, {"id": 82, "Text": "Load", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/load.png", "Children": [{"id": 83, "Text": "CPU Total", "Min": "10.0 %", "Value": "19.4 %", "Max": "72.4 %", "SensorId": "/intelcpu/0/load/0", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 84, "Text": "CPU Core Max", "Min": "22.7 %", "Value": "29.4 %", "Max": "89.7 %", "SensorId": "/intelcpu/0/load/1", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 85, "Text": "CPU Core #1 Thread #1", "Min": "11.0 %", "Value": "23.6 %", "Max": "89.7 %", "SensorId": "/intelcpu/0/load/2", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 86, "Text": "CPU Core #1 Thread #2", "Min": "3.7 %", "Value": "13.2 %", "Max": "61.2 %", "SensorId": "/intelcpu/0/load/3", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 87, "Text": "CPU Core #2 Thread #1", "Min": "8.7 %", "Value": "24.2 %", "Max": "68.9 %", "SensorId": "/intelcpu/0/load/4", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 88, "Text": "CPU Core #2 Thread #2", "Min": "5.7 %", "Value": "16.1 %", "Max": "70.5 %", "SensorId": "/intelcpu/0/load/5", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 89, "Text": "CPU Core #3 Thread #1", "Min": "8.2 %", "Value": "17.1 %", "Max": "79.2 %", "SensorId": "/intelcpu/0/load/6", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 90, "Text": "CPU Core #3 Thread #2", "Min": "5.4 %", "Value": "20.9 %", "Max": "74.4 %", "SensorId": "/intelcpu/0/load/7", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 91, "Text": "CPU Core #4 Thread #1", "Min": "10.6 %", "Value": "24.2 %", "Max": "80.9 %", "SensorId": "/intelcpu/0/load/8", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 92, "Text": "CPU Core #4 Thread #2", "Min": "1.9 %", "Value": "26.3 %", "Max": "65.5 %", "SensorId": "/intelcpu/0/load/9", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 93, "Text": "CPU Core #5 Thread #1", "Min": "3.7 %", "Value": "18.1 %", "Max": "77.6 %", "SensorId": "/intelcpu/0/load/10", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 94, "Text": "CPU Core #5 Thread #2", "Min": "0.8 %", "Value": "11.6 %", "Max": "66.6 %", "SensorId": "/intelcpu/0/load/11", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 95, "Text": "CPU Core #6 Thread #1", "Min": "8.8 %", "Value": "29.4 %", "Max": "77.3 %", "SensorId": "/intelcpu/0/load/12", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 96, "Text": "CPU Core #6 Thread #2", "Min": "1.2 %", "Value": "7.6 %", "Max": "70.6 %", "SensorId": "/intelcpu/0/load/13", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}]}]}, {"id": 97, "Text": "Generic Memory", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/ram.png", "Children": [{"id": 98, "Text": "Load", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/load.png", "Children": [{"id": 99, "Text": "Memory", "Min": "34.0 %", "Value": "34.2 %", "Max": "36.6 %", "SensorId": "/ram/load/0", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 100, "Text": "Virtual Memory", "Min": "37.5 %", "Value": "37.8 %", "Max": "40.4 %", "SensorId": "/ram/load/1", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}]}, {"id": 101, "Text": "Data", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/power.png", "Children": [{"id": 102, "Text": "Memory Used", "Min": "10.8 GB", "Value": "10.9 GB", "Max": "11.6 GB", "SensorId": "/ram/data/0", "Type": "Data", "ImageURL": "images/transparent.png", "Children": []}, {"id": 103, "Text": "Memory Available", "Min": "20.2 GB", "Value": "20.9 GB", "Max": "21.0 GB", "SensorId": "/ram/data/1", "Type": "Data", "ImageURL": "images/transparent.png", "Children": []}, {"id": 104, "Text": "Virtual Memory Used", "Min": "12.7 GB", "Value": "12.8 GB", "Max": "13.7 GB", "SensorId": "/ram/data/2", "Type": "Data", "ImageURL": "images/transparent.png", "Children": []}, {"id": 105, "Text": "Virtual Memory Available", "Min": "20.2 GB", "Value": "21.1 GB", "Max": "21.2 GB", "SensorId": "/ram/data/3", "Type": "Data", "ImageURL": "images/transparent.png", "Children": []}]}]}, {"id": 106, "Text": "NVIDIA GeForce RTX 3080", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/nvidia.png", "Children": [{"id": 107, "Text": "Powers", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/power.png", "Children": [{"id": 108, "Text": "GPU Package", "Min": "36.7 W", "Value": "37.4 W", "Max": "121.3 W", "SensorId": "/gpu-nvidia/0/power/0", "Type": "Power", "ImageURL": "images/transparent.png", "Children": []}]}, {"id": 109, "Text": "Clocks", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/clock.png", "Children": [{"id": 110, "Text": "GPU Core", "Min": "300.0 MHz", "Value": "345.0 MHz", "Max": "1710.0 MHz", "SensorId": "/gpu-nvidia/0/clock/0", "Type": "Clock", "ImageURL": "images/transparent.png", "Children": []}, {"id": 111, "Text": "GPU Memory", "Min": "405.0 MHz", "Value": "405.0 MHz", "Max": "9502.0 MHz", "SensorId": "/gpu-nvidia/0/clock/4", "Type": "Clock", "ImageURL": "images/transparent.png", "Children": []}]}, {"id": 112, "Text": "Temperatures", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/temperature.png", "Children": [{"id": 113, "Text": "GPU Core", "Min": "31.0 °C", "Value": "31.0 °C", "Max": "35.0 °C", "SensorId": "/gpu-nvidia/0/temperature/0", "Type": "Temperature", "ImageURL": "images/transparent.png", "Children": []}, {"id": 114, "Text": "GPU Hot Spot", "Min": "37.9 °C", "Value": "38.1 °C", "Max": "44.6 °C", "SensorId": "/gpu-nvidia/0/temperature/2", "Type": "Temperature", "ImageURL": "images/transparent.png", "Children": []}, {"id": 115, "Text": "GPU Memory Junction", "Min": "38.0 °C", "Value": "40.0 °C", "Max": "44.0 °C", "SensorId": "/gpu-nvidia/0/temperature/3", "Type": "Temperature", "ImageURL": "images/transparent.png", "Children": []}]}, {"id": 116, "Text": "Load", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/load.png", "Children": [{"id": 117, "Text": "GPU Core", "Min": "2.0 %", "Value": "39.0 %", "Max": "48.0 %", "SensorId": "/gpu-nvidia/0/load/0", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 118, "Text": "GPU Memory Controller", "Min": "1.0 %", "Value": "39.0 %", "Max": "45.0 %", "SensorId": "/gpu-nvidia/0/load/1", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 119, "Text": "GPU Video Engine", "Min": "0.0 %", "Value": "31.0 %", "Max": "36.0 %", "SensorId": "/gpu-nvidia/0/load/2", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 120, "Text": "GPU Memory", "Min": "9.7 %", "Value": "16.0 %", "Max": "17.1 %", "SensorId": "/gpu-nvidia/0/load/3", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 121, "Text": "GPU Bus", "Min": "0.0 %", "Value": "7.0 %", "Max": "11.0 %", "SensorId": "/gpu-nvidia/0/load/3", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 122, "Text": "GPU Power", "Min": "10.2 %", "Value": "10.4 %", "Max": "35.6 %", "SensorId": "/gpu-nvidia/0/load/4", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 123, "Text": "GPU Board Power", "Min": "12.0 %", "Value": "12.0 %", "Max": "44.4 %", "SensorId": "/gpu-nvidia/0/load/5", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 124, "Text": "D3D 3D", "Min": "-465.6 %", "Value": "22.4 %", "Max": "22.4 %", "SensorId": "/gpu-nvidia/0/load/7", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 125, "Text": "D3D Copy", "Min": "-62.1 %", "Value": "1.6 %", "Max": "10.6 %", "SensorId": "/gpu-nvidia/0/load/8", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 126, "Text": "D3D Copy", "Min": "-2.3 %", "Value": "0.1 %", "Max": "0.8 %", "SensorId": "/gpu-nvidia/0/load/9", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 127, "Text": "D3D Overlay", "Min": "0.0 %", "Value": "0.0 %", "Max": "0.0 %", "SensorId": "/gpu-nvidia/0/load/10", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 128, "Text": "D3D Security", "Min": "0.0 %", "Value": "0.0 %", "Max": "0.0 %", "SensorId": "/gpu-nvidia/0/load/11", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 129, "Text": "D3D Video Decode", "Min": "0.0 %", "Value": "0.0 %", "Max": "0.0 %", "SensorId": "/gpu-nvidia/0/load/12", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 130, "Text": "D3D Video Encode", "Min": "-942.0 %", "Value": "22.7 %", "Max": "28.2 %", "SensorId": "/gpu-nvidia/0/load/13", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 131, "Text": "D3D Video Encode", "Min": "0.0 %", "Value": "0.0 %", "Max": "0.0 %", "SensorId": "/gpu-nvidia/0/load/14", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 132, "Text": "D3D Video Processing", "Min": "0.0 %", "Value": "0.0 %", "Max": "0.0 %", "SensorId": "/gpu-nvidia/0/load/15", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 133, "Text": "D3D VR", "Min": "0.0 %", "Value": "0.0 %", "Max": "0.0 %", "SensorId": "/gpu-nvidia/0/load/16", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}]}, {"id": 134, "Text": "Fans", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/fan.png", "Children": [{"id": 135, "Text": "GPU Fan 1", "Min": "1282 RPM", "Value": "1319 RPM", "Max": "1321 RPM", "SensorId": "/gpu-nvidia/0/fan/1", "Type": "Fan", "ImageURL": "images/transparent.png", "Children": []}, {"id": 136, "Text": "GPU Fan 2", "Min": "1283 RPM", "Value": "1357 RPM", "Max": "1358 RPM", "SensorId": "/gpu-nvidia/0/fan/2", "Type": "Fan", "ImageURL": "images/transparent.png", "Children": []}]}, {"id": 137, "Text": "Controls", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/control.png", "Children": [{"id": 138, "Text": "GPU Fan 1", "Min": "30.0 %", "Value": "30.0 %", "Max": "31.0 %", "SensorId": "/gpu-nvidia/0/control/1", "Type": "Control", "ImageURL": "images/transparent.png", "Children": []}, {"id": 139, "Text": "GPU Fan 2", "Min": "30.0 %", "Value": "30.0 %", "Max": "31.0 %", "SensorId": "/gpu-nvidia/0/control/2", "Type": "Control", "ImageURL": "images/transparent.png", "Children": []}]}, {"id": 140, "Text": "Data", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/power.png", "Children": [{"id": 141, "Text": "GPU Memory Free", "Min": "10182.0 MB", "Value": "10326.0 MB", "Max": "11092.0 MB", "SensorId": "/gpu-nvidia/0/smalldata/0", "Type": "SmallData", "ImageURL": "images/transparent.png", "Children": []}, {"id": 142, "Text": "GPU Memory Used", "Min": "1195.0 MB", "Value": "1961.0 MB", "Max": "2105.0 MB", "SensorId": "/gpu-nvidia/0/smalldata/1", "Type": "SmallData", "ImageURL": "images/transparent.png", "Children": []}, {"id": 143, "Text": "GPU Memory Total", "Min": "12288.0 MB", "Value": "12288.0 MB", "Max": "12288.0 MB", "SensorId": "/gpu-nvidia/0/smalldata/2", "Type": "SmallData", "ImageURL": "images/transparent.png", "Children": []}, {"id": 144, "Text": "D3D Dedicated Memory Used", "Min": "974.8 MB", "Value": "1773.0 MB", "Max": "1917.9 MB", "SensorId": "/gpu-nvidia/0/smalldata/3", "Type": "SmallData", "ImageURL": "images/transparent.png", "Children": []}, {"id": 145, "Text": "D3D Shared Memory Used", "Min": "136.4 MB", "Value": "222.8 MB", "Max": "242.4 MB", "SensorId": "/gpu-nvidia/0/smalldata/4", "Type": "SmallData", "ImageURL": "images/transparent.png", "Children": []}]}, {"id": 146, "Text": "Throughput", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/throughput.png", "Children": [{"id": 147, "Text": "GPU PCIe Rx", "Min": "100.0 KB/s", "Value": "5.2 MB/s", "Max": "767.7 MB/s", "SensorId": "/gpu-nvidia/0/throughput/0", "Type": "Throughput", "ImageURL": "images/transparent.png", "Children": []}, {"id": 148, "Text": "GPU PCIe Tx", "Min": "400.0 KB/s", "Value": "150.0 MB/s", "Max": "1787.5 MB/s", "SensorId": "/gpu-nvidia/0/throughput/1", "Type": "Throughput", "ImageURL": "images/transparent.png", "Children": []}]}]}, {"id": 149, "Text": "PLEXTOR PX-256M8VC", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/hdd.png", "Children": [{"id": 150, "Text": "Load", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/load.png", "Children": [{"id": 151, "Text": "Used Space", "Min": "71.6 %", "Value": "71.6 %", "Max": "71.6 %", "SensorId": "/ssd/1/load/0", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 152, "Text": "Read Activity", "Min": "0.0 %", "Value": "0.0 %", "Max": "0.0 %", "SensorId": "/ssd/1/load/31", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 153, "Text": "Write Activity", "Min": "0.0 %", "Value": "0.0 %", "Max": "0.9 %", "SensorId": "/ssd/1/load/32", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 154, "Text": "Total Activity", "Min": "0.0 %", "Value": "0.0 %", "Max": "100.0 %", "SensorId": "/ssd/1/load/33", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}]}, {"id": 155, "Text": "Data", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/power.png", "Children": [{"id": 156, "Text": "Host Writes", "Min": "16853.5 GB", "Value": "16853.5 GB", "Max": "16853.5 GB", "SensorId": "/ssd/1/data/0", "Type": "Data", "ImageURL": "images/transparent.png", "Children": []}, {"id": 157, "Text": "Host Reads", "Min": "18034.3 GB", "Value": "18034.3 GB", "Max": "18034.3 GB", "SensorId": "/ssd/1/data/1", "Type": "Data", "ImageURL": "images/transparent.png", "Children": []}]}, {"id": 158, "Text": "Throughput", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/throughput.png", "Children": [{"id": 159, "Text": "Read Rate", "Min": "0.0 KB/s", "Value": "0.0 KB/s", "Max": "0.0 KB/s", "SensorId": "/ssd/1/throughput/34", "Type": "Throughput", "ImageURL": "images/transparent.png", "Children": []}, {"id": 160, "Text": "Write Rate", "Min": "0.0 KB/s", "Value": "0.0 KB/s", "Max": "345.1 KB/s", "SensorId": "/ssd/1/throughput/35", "Type": "Throughput", "ImageURL": "images/transparent.png", "Children": []}]}]}, {"id": 161, "Text": "Samsung SSD 970 EVO Plus 500GB", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/hdd.png", "Children": [{"id": 162, "Text": "Temperatures", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/temperature.png", "Children": [{"id": 163, "Text": "Temperature", "Min": "0.0 °C", "Value": "35.0 °C", "Max": "35.0 °C", "SensorId": "/nvme/3/temperature/0", "Type": "Temperature", "ImageURL": "images/transparent.png", "Children": []}, {"id": 164, "Text": "Temperature 1", "Min": "0.0 °C", "Value": "35.0 °C", "Max": "35.0 °C", "SensorId": "/nvme/3/temperature/6", "Type": "Temperature", "ImageURL": "images/transparent.png", "Children": []}, {"id": 165, "Text": "Temperature 2", "Min": "0.0 °C", "Value": "37.0 °C", "Max": "37.0 °C", "SensorId": "/nvme/3/temperature/7", "Type": "Temperature", "ImageURL": "images/transparent.png", "Children": []}]}, {"id": 166, "Text": "Load", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/load.png", "Children": [{"id": 167, "Text": "Used Space", "Min": "84.1 %", "Value": "84.1 %", "Max": "84.1 %", "SensorId": "/nvme/3/load/0", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 168, "Text": "Read Activity", "Min": "0.0 %", "Value": "1.0 %", "Max": "1.3 %", "SensorId": "/nvme/3/load/31", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 169, "Text": "Write Activity", "Min": "0.0 %", "Value": "0.3 %", "Max": "11.1 %", "SensorId": "/nvme/3/load/32", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 170, "Text": "Total Activity", "Min": "0.0 %", "Value": "1.3 %", "Max": "100.0 %", "SensorId": "/nvme/3/load/33", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}]}, {"id": 171, "Text": "Levels", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/level.png", "Children": [{"id": 172, "Text": "Available Spare", "Min": "0.0 %", "Value": "100.0 %", "Max": "100.0 %", "SensorId": "/nvme/3/level/1", "Type": "Level", "ImageURL": "images/transparent.png", "Children": []}, {"id": 173, "Text": "Available Spare Threshold", "Min": "0.0 %", "Value": "10.0 %", "Max": "10.0 %", "SensorId": "/nvme/3/level/2", "Type": "Level", "ImageURL": "images/transparent.png", "Children": []}, {"id": 174, "Text": "Percentage Used", "Min": "0.0 %", "Value": "5.0 %", "Max": "5.0 %", "SensorId": "/nvme/3/level/3", "Type": "Level", "ImageURL": "images/transparent.png", "Children": []}]}, {"id": 175, "Text": "Data", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/power.png", "Children": [{"id": 176, "Text": "Data Read", "Min": "0.0 GB", "Value": "65667.0 GB", "Max": "65667.0 GB", "SensorId": "/nvme/3/data/4", "Type": "Data", "ImageURL": "images/transparent.png", "Children": []}, {"id": 177, "Text": "Data Written", "Min": "0.0 GB", "Value": "45824.0 GB", "Max": "45824.0 GB", "SensorId": "/nvme/3/data/5", "Type": "Data", "ImageURL": "images/transparent.png", "Children": []}]}, {"id": 178, "Text": "Throughput", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/throughput.png", "Children": [{"id": 179, "Text": "Read Rate", "Min": "0.0 KB/s", "Value": "462.3 KB/s", "Max": "1.0 MB/s", "SensorId": "/nvme/3/throughput/34", "Type": "Throughput", "ImageURL": "images/transparent.png", "Children": []}, {"id": 180, "Text": "Write Rate", "Min": "0.0 KB/s", "Value": "30.6 KB/s", "Max": "26.8 MB/s", "SensorId": "/nvme/3/throughput/35", "Type": "Throughput", "ImageURL": "images/transparent.png", "Children": []}]}]}, {"id": 181, "Text": "TOSHIBA Q200 EX M.2", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/hdd.png", "Children": [{"id": 182, "Text": "Load", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/load.png", "Children": [{"id": 183, "Text": "Used Space", "Min": "84.0 %", "Value": "84.0 %", "Max": "84.0 %", "SensorId": "/hdd/4/load/0", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 184, "Text": "Read Activity", "Min": "0.0 %", "Value": "0.0 %", "Max": "0.0 %", "SensorId": "/hdd/4/load/31", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 185, "Text": "Write Activity", "Min": "0.0 %", "Value": "0.0 %", "Max": "0.2 %", "SensorId": "/hdd/4/load/32", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 186, "Text": "Total Activity", "Min": "0.0 %", "Value": "0.0 %", "Max": "100.0 %", "SensorId": "/hdd/4/load/33", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}]}, {"id": 187, "Text": "Throughput", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/throughput.png", "Children": [{"id": 188, "Text": "Read Rate", "Min": "0.0 KB/s", "Value": "0.0 KB/s", "Max": "0.0 KB/s", "SensorId": "/hdd/4/throughput/34", "Type": "Throughput", "ImageURL": "images/transparent.png", "Children": []}, {"id": 189, "Text": "Write Rate", "Min": "0.0 KB/s", "Value": "0.0 KB/s", "Max": "23.8 KB/s", "SensorId": "/hdd/4/throughput/35", "Type": "Throughput", "ImageURL": "images/transparent.png", "Children": []}]}]}, {"id": 190, "Text": "GemDisk SS2-1281M3C-00 MLC", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/hdd.png", "Children": [{"id": 191, "Text": "Temperatures", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/temperature.png", "Children": [{"id": 192, "Text": "Temperature", "Min": "30.0 °C", "Value": "30.0 °C", "Max": "30.0 °C", "SensorId": "/hdd/0/temperature/0", "Type": "Temperature", "ImageURL": "images/transparent.png", "Children": []}]}, {"id": 193, "Text": "Load", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/load.png", "Children": [{"id": 194, "Text": "Used Space", "Min": "35.5 %", "Value": "35.5 %", "Max": "35.5 %", "SensorId": "/hdd/0/load/0", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 195, "Text": "Read Activity", "Min": "0.0 %", "Value": "0.0 %", "Max": "0.0 %", "SensorId": "/hdd/0/load/31", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 196, "Text": "Write Activity", "Min": "0.0 %", "Value": "0.0 %", "Max": "2.7 %", "SensorId": "/hdd/0/load/32", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 197, "Text": "Total Activity", "Min": "0.0 %", "Value": "0.0 %", "Max": "100.0 %", "SensorId": "/hdd/0/load/33", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}]}, {"id": 198, "Text": "Throughput", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/throughput.png", "Children": [{"id": 199, "Text": "Read Rate", "Min": "0.0 KB/s", "Value": "0.0 KB/s", "Max": "0.0 KB/s", "SensorId": "/hdd/0/throughput/34", "Type": "Throughput", "ImageURL": "images/transparent.png", "Children": []}, {"id": 200, "Text": "Write Rate", "Min": "0.0 KB/s", "Value": "0.0 KB/s", "Max": "332.4 KB/s", "SensorId": "/hdd/0/throughput/35", "Type": "Throughput", "ImageURL": "images/transparent.png", "Children": []}]}]}, {"id": 201, "Text": "Colorful CN600 512GB", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/hdd.png", "Children": [{"id": 202, "Text": "Temperatures", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/temperature.png", "Children": [{"id": 203, "Text": "Temperature", "Min": "0.0 °C", "Value": "33.0 °C", "Max": "33.0 °C", "SensorId": "/nvme/2/temperature/0", "Type": "Temperature", "ImageURL": "images/transparent.png", "Children": []}, {"id": 204, "Text": "Temperature 1", "Min": "0.0 °C", "Value": "33.0 °C", "Max": "33.0 °C", "SensorId": "/nvme/2/temperature/6", "Type": "Temperature", "ImageURL": "images/transparent.png", "Children": []}, {"id": 205, "Text": "Temperature 2", "Min": "0.0 °C", "Value": "29.0 °C", "Max": "29.0 °C", "SensorId": "/nvme/2/temperature/7", "Type": "Temperature", "ImageURL": "images/transparent.png", "Children": []}]}, {"id": 206, "Text": "Load", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/load.png", "Children": [{"id": 207, "Text": "Used Space", "Min": "43.8 %", "Value": "43.8 %", "Max": "43.8 %", "SensorId": "/nvme/2/load/0", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 208, "Text": "Read Activity", "Min": "0.0 %", "Value": "0.0 %", "Max": "0.0 %", "SensorId": "/nvme/2/load/31", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 209, "Text": "Write Activity", "Min": "0.0 %", "Value": "0.0 %", "Max": "0.4 %", "SensorId": "/nvme/2/load/32", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}, {"id": 210, "Text": "Total Activity", "Min": "0.0 %", "Value": "0.0 %", "Max": "100.0 %", "SensorId": "/nvme/2/load/33", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}]}, {"id": 211, "Text": "Levels", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/level.png", "Children": [{"id": 212, "Text": "Available Spare", "Min": "0.0 %", "Value": "100.0 %", "Max": "100.0 %", "SensorId": "/nvme/2/level/1", "Type": "Level", "ImageURL": "images/transparent.png", "Children": []}, {"id": 213, "Text": "Available Spare Threshold", "Min": "0.0 %", "Value": "10.0 %", "Max": "10.0 %", "SensorId": "/nvme/2/level/2", "Type": "Level", "ImageURL": "images/transparent.png", "Children": []}, {"id": 214, "Text": "Percentage Used", "Min": "0.0 %", "Value": "2.0 %", "Max": "2.0 %", "SensorId": "/nvme/2/level/3", "Type": "Level", "ImageURL": "images/transparent.png", "Children": []}]}, {"id": 215, "Text": "Data", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/power.png", "Children": [{"id": 216, "Text": "Data Read", "Min": "0.0 GB", "Value": "13281.0 GB", "Max": "13281.0 GB", "SensorId": "/nvme/2/data/4", "Type": "Data", "ImageURL": "images/transparent.png", "Children": []}, {"id": 217, "Text": "Data Written", "Min": "0.0 GB", "Value": "11050.0 GB", "Max": "11050.0 GB", "SensorId": "/nvme/2/data/5", "Type": "Data", "ImageURL": "images/transparent.png", "Children": []}]}, {"id": 218, "Text": "Throughput", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/throughput.png", "Children": [{"id": 219, "Text": "Read Rate", "Min": "0.0 KB/s", "Value": "0.0 KB/s", "Max": "0.0 KB/s", "SensorId": "/nvme/2/throughput/34", "Type": "Throughput", "ImageURL": "images/transparent.png", "Children": []}, {"id": 220, "Text": "Write Rate", "Min": "0.0 KB/s", "Value": "0.0 KB/s", "Max": "20.4 KB/s", "SensorId": "/nvme/2/throughput/35", "Type": "Throughput", "ImageURL": "images/transparent.png", "Children": []}]}]}, {"id": 221, "Text": "vEthernet (Default Switch)", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/nic.png", "Children": [{"id": 222, "Text": "Load", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/load.png", "Children": [{"id": 223, "Text": "Network Utilization", "Min": "0.0 %", "Value": "0.0 %", "Max": "0.0 %", "SensorId": "/nic/%7BF0CCB6F3-28D1-4E42-863B-807BEB8214B6%7D/load/1", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}]}, {"id": 224, "Text": "Data", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/power.png", "Children": [{"id": 225, "Text": "Data Uploaded", "Min": "0.0 GB", "Value": "0.0 GB", "Max": "0.0 GB", "SensorId": "/nic/%7BF0CCB6F3-28D1-4E42-863B-807BEB8214B6%7D/data/2", "Type": "Data", "ImageURL": "images/transparent.png", "Children": []}, {"id": 226, "Text": "Data Downloaded", "Min": "0.0 GB", "Value": "0.0 GB", "Max": "0.0 GB", "SensorId": "/nic/%7BF0CCB6F3-28D1-4E42-863B-807BEB8214B6%7D/data/3", "Type": "Data", "ImageURL": "images/transparent.png", "Children": []}]}, {"id": 227, "Text": "Throughput", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/throughput.png", "Children": [{"id": 228, "Text": "Upload Speed", "Min": "0.0 KB/s", "Value": "0.0 KB/s", "Max": "14.0 KB/s", "SensorId": "/nic/%7BF0CCB6F3-28D1-4E42-863B-807BEB8214B6%7D/throughput/7", "Type": "Throughput", "ImageURL": "images/transparent.png", "Children": []}, {"id": 229, "Text": "Download Speed", "Min": "0.0 KB/s", "Value": "0.0 KB/s", "Max": "0.0 KB/s", "SensorId": "/nic/%7BF0CCB6F3-28D1-4E42-863B-807BEB8214B6%7D/throughput/8", "Type": "Throughput", "ImageURL": "images/transparent.png", "Children": []}]}]}, {"id": 230, "Text": "蓝牙网络连接 12", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/nic.png", "Children": [{"id": 231, "Text": "Load", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/load.png", "Children": [{"id": 232, "Text": "Network Utilization", "Min": "0.0 %", "Value": "0.0 %", "Max": "0.0 %", "SensorId": "/nic/%7B0D96317D-9314-4AA6-87E8-2AA62FD14B84%7D/load/1", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}]}, {"id": 233, "Text": "Data", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/power.png", "Children": [{"id": 234, "Text": "Data Uploaded", "Min": "0.0 GB", "Value": "0.0 GB", "Max": "0.0 GB", "SensorId": "/nic/%7B0D96317D-9314-4AA6-87E8-2AA62FD14B84%7D/data/2", "Type": "Data", "ImageURL": "images/transparent.png", "Children": []}, {"id": 235, "Text": "Data Downloaded", "Min": "0.0 GB", "Value": "0.0 GB", "Max": "0.0 GB", "SensorId": "/nic/%7B0D96317D-9314-4AA6-87E8-2AA62FD14B84%7D/data/3", "Type": "Data", "ImageURL": "images/transparent.png", "Children": []}]}, {"id": 236, "Text": "Throughput", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/throughput.png", "Children": [{"id": 237, "Text": "Upload Speed", "Min": "0.0 KB/s", "Value": "0.0 KB/s", "Max": "0.0 KB/s", "SensorId": "/nic/%7B0D96317D-9314-4AA6-87E8-2AA62FD14B84%7D/throughput/7", "Type": "Throughput", "ImageURL": "images/transparent.png", "Children": []}, {"id": 238, "Text": "Download Speed", "Min": "0.0 KB/s", "Value": "0.0 KB/s", "Max": "0.0 KB/s", "SensorId": "/nic/%7B0D96317D-9314-4AA6-87E8-2AA62FD14B84%7D/throughput/8", "Type": "Throughput", "ImageURL": "images/transparent.png", "Children": []}]}]}, {"id": 239, "Text": "以太网 4", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/nic.png", "Children": [{"id": 240, "Text": "Load", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/load.png", "Children": [{"id": 241, "Text": "Network Utilization", "Min": "0.0 %", "Value": "0.0 %", "Max": "0.0 %", "SensorId": "/nic/%7BAA5014C3-8F53-4AAF-B9E6-5F93DC581965%7D/load/1", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}]}, {"id": 242, "Text": "Data", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/power.png", "Children": [{"id": 243, "Text": "Data Uploaded", "Min": "0.0 GB", "Value": "0.0 GB", "Max": "0.0 GB", "SensorId": "/nic/%7BAA5014C3-8F53-4AAF-B9E6-5F93DC581965%7D/data/2", "Type": "Data", "ImageURL": "images/transparent.png", "Children": []}, {"id": 244, "Text": "Data Downloaded", "Min": "0.0 GB", "Value": "0.0 GB", "Max": "0.0 GB", "SensorId": "/nic/%7BAA5014C3-8F53-4AAF-B9E6-5F93DC581965%7D/data/3", "Type": "Data", "ImageURL": "images/transparent.png", "Children": []}]}, {"id": 245, "Text": "Throughput", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/throughput.png", "Children": [{"id": 246, "Text": "Upload Speed", "Min": "0.0 KB/s", "Value": "0.0 KB/s", "Max": "0.0 KB/s", "SensorId": "/nic/%7BAA5014C3-8F53-4AAF-B9E6-5F93DC581965%7D/throughput/7", "Type": "Throughput", "ImageURL": "images/transparent.png", "Children": []}, {"id": 247, "Text": "Download Speed", "Min": "0.0 KB/s", "Value": "0.0 KB/s", "Max": "0.0 KB/s", "SensorId": "/nic/%7BAA5014C3-8F53-4AAF-B9E6-5F93DC581965%7D/throughput/8", "Type": "Throughput", "ImageURL": "images/transparent.png", "Children": []}]}]}, {"id": 248, "Text": "以太网 5", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/nic.png", "Children": [{"id": 249, "Text": "Load", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/load.png", "Children": [{"id": 250, "Text": "Network Utilization", "Min": "0.0 %", "Value": "0.0 %", "Max": "0.9 %", "SensorId": "/nic/%7B40E722A1-D6E5-46D8-A63A-E20520FA2E1F%7D/load/1", "Type": "Load", "ImageURL": "images/transparent.png", "Children": []}]}, {"id": 251, "Text": "Data", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/power.png", "Children": [{"id": 252, "Text": "Data Uploaded", "Min": "1.1 GB", "Value": "1.1 GB", "Max": "1.1 GB", "SensorId": "/nic/%7B40E722A1-D6E5-46D8-A63A-E20520FA2E1F%7D/data/2", "Type": "Data", "ImageURL": "images/transparent.png", "Children": []}, {"id": 253, "Text": "Data Downloaded", "Min": "1.2 GB", "Value": "1.2 GB", "Max": "1.2 GB", "SensorId": "/nic/%7B40E722A1-D6E5-46D8-A63A-E20520FA2E1F%7D/data/3", "Type": "Data", "ImageURL": "images/transparent.png", "Children": []}]}, {"id": 254, "Text": "Throughput", "Min": "", "Value": "", "Max": "", "ImageURL": "images_icon/throughput.png", "Children": [{"id": 255, "Text": "Upload Speed", "Min": "5.2 KB/s", "Value": "93.6 KB/s", "Max": "1.6 MB/s", "SensorId": "/nic/%7B40E722A1-D6E5-46D8-A63A-E20520FA2E1F%7D/throughput/7", "Type": "Throughput", "ImageURL": "images/transparent.png", "Children": []}, {"id": 256, "Text": "Download Speed", "Min": "5.0 KB/s", "Value": "55.4 KB/s", "Max": "2.3 MB/s", "SensorId": "/nic/%7B40E722A1-D6E5-46D8-A63A-E20520FA2E1F%7D/throughput/8", "Type": "Throughput", "ImageURL": "images/transparent.png", "Children": []}]}]}]}]}