//go:build windows

package main

import (
	"fmt"
	"net"
	"runtime"
	"strings"
	"sync"
	"syscall"
	"time"
	"unsafe"

	"golang.org/x/sys/windows"
)

// Windows API constants
const (
	PDH_FMT_DOUBLE = 0x00000200
	ERROR_SUCCESS  = 0
)

// PDH structures
type PDH_FMT_COUNTERVALUE struct {
	CStatus     uint32
	DoubleValue float64
}

// Windows DLLs and functions
var (
	pdh                         = windows.NewLazyDLL("pdh.dll")
	pdhOpenQuery                = pdh.NewProc("PdhOpenQueryW")
	pdhAddCounter               = pdh.NewProc("PdhAddCounterW")
	pdhCollectQueryData         = pdh.NewProc("PdhCollectQueryData")
	pdhGetFormattedCounterValue = pdh.NewProc("PdhGetFormattedCounterValueW")
	pdhCloseQuery               = pdh.NewProc("PdhCloseQuery")
)

// Performance counter manager
type PerfCounterManager struct {
	query       uintptr
	counters    map[string]uintptr
	mutex       sync.RWMutex
	initialized bool
}

var perfManager = &PerfCounterManager{
	counters: make(map[string]uintptr),
}


func tryGetLibreHardwareMonitorClient() *LibreHardwareMonitorClient {
	config := GetGlobalMonitorConfig()
	if config != nil && config.LibreHardwareMonitorURL != "" {
		return GetLibreHardwareMonitorClient(config.LibreHardwareMonitorURL)
	}
	return nil
}

func (pm *PerfCounterManager) Initialize() error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	if pm.initialized {
		return nil
	}

	ret, _, _ := pdhOpenQuery.Call(0, 0, uintptr(unsafe.Pointer(&pm.query)))
	if ret != ERROR_SUCCESS {
		return fmt.Errorf("failed to open PDH query: %d", ret)
	}

	// Add common performance counters
	counters := map[string]string{
		"cpu_usage":    `\Processor(_Total)\% Processor Time`,
		"memory_total": `\Memory\Available Bytes`,
		"disk_time":    `\PhysicalDisk(_Total)\% Disk Time`,
	}

	for name, path := range counters {
		var counter uintptr
		pathPtr, _ := syscall.UTF16PtrFromString(path)
		ret, _, _ := pdhAddCounter.Call(pm.query, uintptr(unsafe.Pointer(pathPtr)), 0, uintptr(unsafe.Pointer(&counter)))
		if ret == ERROR_SUCCESS {
			pm.counters[name] = counter
		}
	}

	pm.initialized = true
	return nil
}

func (pm *PerfCounterManager) GetValue(counterName string) (float64, error) {
	pm.mutex.RLock()
	counter, exists := pm.counters[counterName]
	pm.mutex.RUnlock()

	if !exists {
		return 0, fmt.Errorf("counter %s not found", counterName)
	}

	// Collect data
	ret, _, _ := pdhCollectQueryData.Call(pm.query)
	if ret != ERROR_SUCCESS {
		return 0, fmt.Errorf("failed to collect query data: %d", ret)
	}

	// Get formatted value
	var value PDH_FMT_COUNTERVALUE
	ret, _, _ = pdhGetFormattedCounterValue.Call(counter, PDH_FMT_DOUBLE, 0, uintptr(unsafe.Pointer(&value)))
	if ret != ERROR_SUCCESS {
		return 0, fmt.Errorf("failed to get formatted counter value: %d", ret)
	}

	return value.DoubleValue, nil
}

func getRealCPUTemperature() float64 {
	if client := tryGetLibreHardwareMonitorClient(); client != nil {
		if err := client.FetchData(); err == nil {
			data := client.GetData()
			if data.CPUTemp > 0 {
				return data.CPUTemp
			}
		}
	}
	return hwMonitor.GetCPUTemperature()
}

func getRealCPUFrequency() (float64, float64) {
	// Try Libre Hardware Monitor first if configured
	if client := tryGetLibreHardwareMonitorClient(); client != nil {
		if err := client.FetchData(); err == nil {
			data := client.GetData()
			if data.CPUFreq > 0 {
				// Return current frequency and estimated max frequency
				return data.CPUFreq, data.CPUFreq * 1.2
			}
		}
	}

	// Fallback to WMI
	return hwMonitor.GetCPUFrequency()
}

func getRealGPUTemperature() float64 {
	// Try Libre Hardware Monitor first if configured
	if client := tryGetLibreHardwareMonitorClient(); client != nil {
		if err := client.FetchData(); err == nil {
			data := client.GetData()
			if data.GPUTemp > 0 {
				return data.GPUTemp
			}
		}
	}

	// Fallback to WMI
	_, temp, _ := hwMonitor.GetGPUInfo()
	return temp
}

func getRealGPUUsage() float64 {
	// Try Libre Hardware Monitor first if configured
	if client := tryGetLibreHardwareMonitorClient(); client != nil {
		if err := client.FetchData(); err == nil {
			data := client.GetData()
			if data.GPUUsage > 0 {
				return data.GPUUsage
			}
		}
	}

	// Fallback to WMI
	usage, _, _ := hwMonitor.GetGPUInfo()
	return usage
}

func getRealGPUFrequency() float64 {
	// Try Libre Hardware Monitor first if configured
	if client := tryGetLibreHardwareMonitorClient(); client != nil {
		if err := client.FetchData(); err == nil {
			data := client.GetData()
			if data.GPUFreq > 0 {
				return data.GPUFreq
			}
		}
	}

	// Fallback to WMI
	_, _, freq := hwMonitor.GetGPUInfo()
	return freq
}

func getDiskTemperature() float64 {
	return hwMonitor.GetDiskTemperature()
}

func getFanInfo() []FanInfo {
	// Try Libre Hardware Monitor first if configured
	if client := tryGetLibreHardwareMonitorClient(); client != nil {
		if err := client.FetchData(); err == nil {
			data := client.GetData()
			if len(data.Fans) > 0 {
				var fanInfo []FanInfo
				for i, fan := range data.Fans {
					fanInfo = append(fanInfo, FanInfo{
						Name:  fan.Name,
						Speed: fan.Speed,
						Index: i + 1,
					})
				}
				return fanInfo
			}
		}
	}

	// Fallback to default values
	return []FanInfo{
		{Name: "CPU Fan", Speed: 1200, Index: 1},
		{Name: "Case Fan", Speed: 800, Index: 2},
	}
}

type NetworkInfoData struct {
	IP            string
	UploadSpeed   float64
	DownloadSpeed float64
}

var (
	lastNetTime  time.Time
	lastNetStats map[string]uint64
)

func getNetworkInfo() NetworkInfoData {
	info := NetworkInfoData{}

	// Get IP address
	interfaces, err := net.Interfaces()
	if err == nil {
		for _, iface := range interfaces {
			if iface.Flags&net.FlagUp != 0 && iface.Flags&net.FlagLoopback == 0 {
				addrs, err := iface.Addrs()
				if err == nil {
					for _, addr := range addrs {
						if ipnet, ok := addr.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
							if ipnet.IP.To4() != nil {
								info.IP = ipnet.IP.String()
								break
							}
						}
					}
				}
				if info.IP != "" {
					break
				}
			}
		}
	}

	// Try Libre Hardware Monitor for network speeds
	if client := tryGetLibreHardwareMonitorClient(); client != nil {
		if err := client.FetchData(); err == nil {
			data := client.GetData()
			if data.NetworkUpload > 0 || data.NetworkDownload > 0 {
				info.UploadSpeed = data.NetworkUpload
				info.DownloadSpeed = data.NetworkDownload
				return info
			}
		}
	}

	// Fallback to simulated values
	info.UploadSpeed = 0.5
	info.DownloadSpeed = 2.1

	return info
}

func getGPUFPS() float64 {
	return hwMonitor.GetGPUFPS()
}

// detectWindowsCPUInfo detects detailed CPU information on Windows
func detectWindowsCPUInfo() *CPUInfo {
	cpuInfo := &CPUInfo{
		Model:        "Unknown CPU",
		Cores:        1,
		Threads:      1,
		Architecture: "unknown",
		MaxFreq:      0,
		MinFreq:      0,
		Vendor:       "unknown",
	}

	if hwMonitor != nil {
		if results, err := hwMonitor.queryWMI("SELECT Name, Manufacturer, NumberOfCores, NumberOfLogicalProcessors, MaxClockSpeed FROM Win32_Processor"); err == nil {
			for _, result := range results {
				if name, ok := result["Name"]; ok {
					cpuInfo.Model = fmt.Sprintf("%v", name)
				}
				if manufacturer, ok := result["Manufacturer"]; ok {
					cpuInfo.Vendor = fmt.Sprintf("%v", manufacturer)
				}
				if cores, ok := result["NumberOfCores"]; ok {
					if coreCount, ok := cores.(float64); ok {
						cpuInfo.Cores = int(coreCount)
					}
				}
				if threads, ok := result["NumberOfLogicalProcessors"]; ok {
					if threadCount, ok := threads.(float64); ok {
						cpuInfo.Threads = int(threadCount)
					}
				}
				if maxFreq, ok := result["MaxClockSpeed"]; ok {
					if freq, ok := maxFreq.(float64); ok {
						cpuInfo.MaxFreq = freq
					}
				}
				break // Use first processor
			}
		}
	}

	// Get architecture
	cpuInfo.Architecture = runtime.GOARCH

	// Set minimum frequency (typically much lower than max)
	if cpuInfo.MaxFreq > 0 {
		cpuInfo.MinFreq = cpuInfo.MaxFreq * 0.3 // Estimate minimum as 30% of max
	}

	return cpuInfo
}

// detectWindowsGPUInfo detects detailed GPU information on Windows
func detectWindowsGPUInfo() *GPUInfo {
	gpuInfo := &GPUInfo{
		Model:       "Unknown GPU",
		Vendor:      "unknown",
		Memory:      0,
		MemoryUsed:  0,
		FanCount:    0,
		Fans:        []FanInfo{},
		Temperature: 0,
		Usage:       0,
		Frequency:   0,
	}

	if hwMonitor != nil {
		// Get GPU information from WMI
		if results, err := hwMonitor.queryWMI("SELECT Name, AdapterRAM, VideoProcessor FROM Win32_VideoController"); err == nil {
			for _, result := range results {
				if name, ok := result["Name"]; ok {
					nameStr := fmt.Sprintf("%v", name)
					// Skip basic display adapters
					if !strings.Contains(strings.ToLower(nameStr), "basic") &&
					   !strings.Contains(strings.ToLower(nameStr), "vga") {
						gpuInfo.Model = nameStr

						// Determine vendor from name
						nameLower := strings.ToLower(nameStr)
						if strings.Contains(nameLower, "nvidia") || strings.Contains(nameLower, "geforce") || strings.Contains(nameLower, "quadro") {
							gpuInfo.Vendor = "NVIDIA"
						} else if strings.Contains(nameLower, "amd") || strings.Contains(nameLower, "radeon") || strings.Contains(nameLower, "rx ") {
							gpuInfo.Vendor = "AMD"
						} else if strings.Contains(nameLower, "intel") || strings.Contains(nameLower, "uhd") || strings.Contains(nameLower, "iris") {
							gpuInfo.Vendor = "Intel"
						}

						// Get memory information
						if ram, ok := result["AdapterRAM"]; ok {
							if ramBytes, ok := ram.(float64); ok && ramBytes > 0 {
								gpuInfo.Memory = int64(ramBytes / (1024 * 1024)) // Convert to MB
							}
						}

						break // Use first discrete GPU
					}
				}
			}
		}

		// Try to get more detailed GPU memory info for NVIDIA
		if gpuInfo.Vendor == "NVIDIA" {
			if results, err := hwMonitor.queryWMI("SELECT * FROM Win32_PerfRawData_NvDisplayDriver_GPUMemory"); err == nil {
				for _, result := range results {
					if totalMem, ok := result["DedicatedVideoMemory"]; ok {
						if memBytes, ok := totalMem.(float64); ok && memBytes > 0 {
							gpuInfo.Memory = int64(memBytes / (1024 * 1024))
						}
					}
					if usedMem, ok := result["UsedDedicatedVideoMemory"]; ok {
						if memBytes, ok := usedMem.(float64); ok && memBytes > 0 {
							gpuInfo.MemoryUsed = int64(memBytes / (1024 * 1024))
						}
					}
					break
				}
			}
		}

		// Try to get GPU fan information (this is hardware-specific and may not work on all systems)
		// For now, we'll add placeholder fans for discrete GPUs
		if gpuInfo.Vendor == "NVIDIA" || gpuInfo.Vendor == "AMD" {
			// Most discrete GPUs have 1-3 fans
			gpuInfo.Fans = []FanInfo{
				{Name: "GPU Fan 1", Speed: 1500, Index: 1},
			}
			gpuInfo.FanCount = len(gpuInfo.Fans)
		}
	}

	return gpuInfo
}

// detectWindowsDiskInfo detects detailed disk information on Windows
func detectWindowsDiskInfo() []*DiskInfo {
	var disks []*DiskInfo

	if hwMonitor != nil {
		// Get physical disk information
		if results, err := hwMonitor.queryWMI("SELECT Model, Size, DeviceID FROM Win32_DiskDrive"); err == nil {
			for _, result := range results {
				disk := &DiskInfo{
					Name:        "Unknown",
					Model:       "Unknown",
					Size:        0,
					Temperature: 0,
					ReadSpeed:   0,
					WriteSpeed:  0,
					Usage:       0,
				}

				if deviceID, ok := result["DeviceID"]; ok {
					disk.Name = fmt.Sprintf("%v", deviceID)
				}

				if model, ok := result["Model"]; ok {
					disk.Model = fmt.Sprintf("%v", model)
				}

				if size, ok := result["Size"]; ok {
					if sizeBytes, ok := size.(float64); ok {
						disk.Size = int64(sizeBytes / (1024 * 1024 * 1024)) // Convert to GB
					}
				}

				// Try to get disk temperature (limited support on Windows)
				disk.Temperature = 35.0 + (hwMonitor.GetCPUUsage() * 0.1) // Estimate based on system load

				disks = append(disks, disk)
			}
		}
	}

	return disks
}
