{"name": "windows", "width": 480, "height": 320, "font_sizes": {"min_label": 10, "def_label": 12, "default": 16, "small": 10, "large": 16}, "font_families": ["DejaVu Sans Mono", "Liberation Mono", "Consolas", "Monaco", "<PERSON><PERSON>", "Ubuntu Mono", "Courier New", "monospace"], "output_type": "ax206usb", "output_file": "", "refresh_interval": 1000, "history_size": 150, "network_interface": "auto", "libre_hardware_monitor_url": "http://192.168.11.19:8085", "colors": {"default_text": "#f8fafc", "default_background": "#0f172a", "header_text": "#38bdf8", "header_background": "#020617", "progress_background": "#334155", "value_normal": "#22d3ee", "progress_fill": "#06b6d4", "chart_line": "#3b82f6", "value_warning": "#fbbf24", "value_critical": "#ef4444", "border_color": "#475569"}, "items": [{"type": "text", "x": 10, "y": 10, "width": 460, "height": 20, "text": "System Monitor - Windows with Libre Hardware Monitor", "font_size": "def_label", "color": "header_text", "background_color": "header_background", "alignment": "center"}, {"type": "value", "x": 10, "y": 40, "width": 110, "height": 30, "monitor": "cpu_usage", "label": "CPU", "font_size": "default"}, {"type": "value", "x": 130, "y": 40, "width": 110, "height": 30, "monitor": "cpu_temp", "label": "CPU Temp", "font_size": "default"}, {"type": "value", "x": 250, "y": 40, "width": 110, "height": 30, "monitor": "cpu_freq", "label": "CPU Freq", "font_size": "default"}, {"type": "value", "x": 370, "y": 40, "width": 100, "height": 30, "monitor": "memory_usage", "label": "Memory", "font_size": "default"}, {"type": "value", "x": 10, "y": 80, "width": 110, "height": 30, "monitor": "gpu_usage", "label": "GPU", "font_size": "default"}, {"type": "value", "x": 130, "y": 80, "width": 110, "height": 30, "monitor": "gpu_temp", "label": "GPU Temp", "font_size": "default"}, {"type": "value", "x": 250, "y": 80, "width": 110, "height": 30, "monitor": "gpu_freq", "label": "GPU Freq", "font_size": "default"}, {"type": "value", "x": 370, "y": 80, "width": 100, "height": 30, "monitor": "memory_used", "label": "<PERSON><PERSON>", "font_size": "default"}, {"type": "value", "x": 10, "y": 120, "width": 110, "height": 30, "monitor": "cpu_fan_speed", "label": "CPU Fan", "font_size": "default"}, {"type": "value", "x": 130, "y": 120, "width": 110, "height": 30, "monitor": "gpu_fan_speed", "label": "GPU Fan", "font_size": "default"}, {"type": "value", "x": 250, "y": 120, "width": 110, "height": 30, "monitor": "net_upload", "label": "Upload", "font_size": "default"}, {"type": "value", "x": 370, "y": 120, "width": 100, "height": 30, "monitor": "net_download", "label": "Download", "font_size": "default"}, {"type": "chart", "x": 10, "y": 160, "width": 220, "height": 80, "monitor": "cpu_usage", "label": "CPU Usage History", "font_size": "small"}, {"type": "chart", "x": 250, "y": 160, "width": 220, "height": 80, "monitor": "memory_usage", "label": "Memory Usage History", "font_size": "small"}, {"type": "progress", "x": 10, "y": 250, "width": 220, "height": 20, "monitor": "cpu_temp", "label": "CPU Temperature", "font_size": "small"}, {"type": "progress", "x": 250, "y": 250, "width": 220, "height": 20, "monitor": "gpu_temp", "label": "GPU Temperature", "font_size": "small"}, {"type": "text", "x": 10, "y": 280, "width": 460, "height": 30, "text": "Data from Libre Hardware Monitor", "font_size": "small", "color": "value_normal", "alignment": "center"}]}