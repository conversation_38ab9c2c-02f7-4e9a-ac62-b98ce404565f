{"name": "full", "width": 480, "height": 320, "font_sizes": {"min_label": 8, "def_label": 10, "default": 12, "small": 8, "large": 14}, "font_families": ["DejaVu Sans Mono", "Liberation Mono", "Consolas", "Monaco", "<PERSON><PERSON>", "Ubuntu Mono", "Courier New", "monospace"], "output_type": "ax206usb", "output_file": "", "refresh_interval": 1000, "history_size": 200, "network_interface": "auto", "libre_hardware_monitor_url": "http://192.168.11.19:8085", "colors": {"default_text": "#f8fafc", "default_background": "#0f172a", "header_text": "#38bdf8", "header_background": "#020617", "progress_background": "#334155", "value_normal": "#22d3ee", "progress_fill": "#06b6d4", "chart_line": "#3b82f6", "value_warning": "#fbbf24", "value_critical": "#ef4444", "border_color": "#475569"}, "color_thresholds": {"cpu_temp": {"low_threshold": 60, "high_threshold": 75, "low_color": "#22c55e", "medium_color": "#eab308", "high_color": "#ef4444"}, "gpu_temp": {"low_threshold": 60, "high_threshold": 75, "low_color": "#22c55e", "medium_color": "#eab308", "high_color": "#ef4444"}, "disk_temp": {"low_threshold": 50, "high_threshold": 65, "low_color": "#22c55e", "medium_color": "#eab308", "high_color": "#ef4444"}, "cpu_usage": {"low_threshold": 60, "high_threshold": 75, "low_color": "#22c55e", "medium_color": "#eab308", "high_color": "#ef4444"}, "memory_usage": {"low_threshold": 60, "high_threshold": 75, "low_color": "#22c55e", "medium_color": "#eab308", "high_color": "#ef4444"}, "gpu_usage": {"low_threshold": 60, "high_threshold": 75, "low_color": "#22c55e", "medium_color": "#eab308", "high_color": "#ef4444"}, "net_upload": {"low_threshold": 10, "high_threshold": 50, "low_color": "#22c55e", "medium_color": "#eab308", "high_color": "#ef4444"}, "net_download": {"low_threshold": 10, "high_threshold": 50, "low_color": "#22c55e", "medium_color": "#eab308", "high_color": "#ef4444"}}, "items": [{"type": "value", "monitor": "current_time", "x": 5, "y": 5, "width": 155, "height": 56, "font_size": 12, "bg": "#0f172a"}, {"type": "value", "monitor": "load_avg", "x": 162, "y": 5, "width": 155, "height": 56, "font_size": 12, "bg": "#0f172a"}, {"type": "value", "monitor": "disk_temp", "x": 319, "y": 5, "width": 156, "height": 56, "font_size": 12, "bg": "#0f172a"}, {"type": "value", "monitor": "cpu_usage", "x": 5, "y": 63, "width": 155, "height": 56, "font_size": 12, "bg": "#0f172a"}, {"type": "value", "monitor": "cpu_temp", "x": 162, "y": 63, "width": 155, "height": 56, "font_size": 12, "bg": "#0f172a"}, {"type": "value", "monitor": "cpu_freq", "x": 319, "y": 63, "width": 156, "height": 56, "font_size": 12, "bg": "#0f172a"}, {"type": "value", "monitor": "memory_usage", "x": 5, "y": 121, "width": 155, "height": 56, "font_size": 12, "bg": "#0f172a"}, {"type": "value", "monitor": "memory_used", "x": 162, "y": 121, "width": 155, "height": 56, "font_size": 12, "bg": "#0f172a"}, {"type": "value", "monitor": "memory_total", "x": 319, "y": 121, "width": 156, "height": 56, "font_size": 12, "bg": "#0f172a"}, {"type": "value", "monitor": "gpu_usage", "x": 5, "y": 179, "width": 155, "height": 56, "font_size": 12, "bg": "#0f172a"}, {"type": "value", "monitor": "gpu_temp", "x": 162, "y": 179, "width": 155, "height": 56, "font_size": 12, "bg": "#0f172a"}, {"type": "value", "monitor": "gpu_freq", "x": 319, "y": 179, "width": 156, "height": 56, "font_size": 12, "bg": "#0f172a"}, {"type": "value", "monitor": "net_ip", "x": 5, "y": 237, "width": 155, "height": 56, "font_size": 12, "bg": "#0f172a"}, {"type": "value", "monitor": "net_upload", "x": 162, "y": 237, "width": 155, "height": 56, "font_size": 12, "bg": "#0f172a"}, {"type": "value", "monitor": "net_download", "x": 319, "y": 237, "width": 156, "height": 56, "font_size": 12, "bg": "#0f172a"}, {"type": "text", "text": "Repository: https://github.com/yukunyi/ax206monitor", "x": 5, "y": 295, "width": 470, "height": 20, "font_size": 10, "bg": "#1e293b"}]}