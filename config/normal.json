{"name": "normal", "width": 480, "height": 320, "font_sizes": {"min_label": 10, "def_label": 12, "default": 16, "small": 10, "large": 16}, "font_families": ["DejaVu Sans Mono", "Liberation Mono", "Consolas", "Monaco", "<PERSON><PERSON>", "Ubuntu Mono", "Courier New", "monospace"], "output_type": "ax206usb", "output_file": "", "refresh_interval": 1000, "history_size": 150, "network_interface": "auto", "colors": {"default_text": "#f8fafc", "default_background": "#0f172a", "header_text": "#38bdf8", "header_background": "#020617", "progress_background": "#334155", "value_normal": "#22d3ee", "progress_fill": "#06b6d4", "chart_line": "#3b82f6", "value_warning": "#fbbf24", "value_critical": "#ef4444", "border_color": "#475569"}, "color_thresholds": {"cpu_temp": {"low_threshold": 60, "high_threshold": 75, "low_color": "#22c55e", "medium_color": "#eab308", "high_color": "#ef4444"}, "gpu_temp": {"low_threshold": 60, "high_threshold": 75, "low_color": "#22c55e", "medium_color": "#eab308", "high_color": "#ef4444"}, "disk_temp": {"low_threshold": 50, "high_threshold": 65, "low_color": "#22c55e", "medium_color": "#eab308", "high_color": "#ef4444"}, "cpu_usage": {"low_threshold": 60, "high_threshold": 75, "low_color": "#22c55e", "medium_color": "#eab308", "high_color": "#ef4444"}, "memory_usage": {"low_threshold": 60, "high_threshold": 75, "low_color": "#22c55e", "medium_color": "#eab308", "high_color": "#ef4444"}, "gpu_usage": {"low_threshold": 60, "high_threshold": 75, "low_color": "#22c55e", "medium_color": "#eab308", "high_color": "#ef4444"}, "net_upload": {"low_threshold": 10, "high_threshold": 50, "low_color": "#22c55e", "medium_color": "#eab308", "high_color": "#ef4444"}, "net_download": {"low_threshold": 10, "high_threshold": 50, "low_color": "#22c55e", "medium_color": "#eab308", "high_color": "#ef4444"}}, "items": [{"type": "value", "monitor": "current_time", "x": 5, "y": 5, "width": 235, "height": 20, "font_size": 12, "bg": "#020617", "label": false}, {"type": "value", "monitor": "net_ip", "x": 245, "y": 5, "width": 230, "height": 20, "font_size": 12, "bg": "#020617"}, {"type": "big_value", "monitor": "cpu_temp", "x": 5, "y": 30, "width": 155, "height": 65, "font_size": 24, "bg": "#0f172a"}, {"type": "big_value", "monitor": "gpu_temp", "x": 165, "y": 30, "width": 155, "height": 65, "font_size": 24, "bg": "#0f172a"}, {"type": "big_value", "monitor": "disk_temp", "x": 325, "y": 30, "width": 150, "height": 65, "font_size": 24, "bg": "#0f172a"}, {"type": "value", "monitor": "cpu_usage", "x": 5, "y": 100, "width": 155, "height": 50, "font_size": 16, "bg": "#0f172a"}, {"type": "value", "monitor": "memory_usage", "x": 165, "y": 100, "width": 155, "height": 50, "font_size": 16, "bg": "#0f172a"}, {"type": "value", "monitor": "gpu_usage", "x": 325, "y": 100, "width": 150, "height": 50, "font_size": 16, "bg": "#0f172a"}, {"type": "big_value", "monitor": "disk_read_speed", "x": 5, "y": 155, "width": 155, "height": 55, "font_size": 18, "bg": "#0f172a"}, {"type": "big_value", "monitor": "disk_write_speed", "x": 165, "y": 155, "width": 155, "height": 55, "font_size": 18, "bg": "#0f172a"}, {"type": "big_value", "monitor": "net_upload", "x": 325, "y": 155, "width": 150, "height": 55, "font_size": 16, "bg": "#0f172a"}, {"type": "chart", "monitor": "cpu_usage", "x": 5, "y": 215, "width": 155, "height": 50, "history": true}, {"type": "chart", "monitor": "memory_usage", "x": 165, "y": 215, "width": 155, "height": 50, "history": true}, {"type": "big_value", "monitor": "net_download", "x": 325, "y": 215, "width": 150, "height": 50, "font_size": 16, "bg": "#0f172a"}, {"type": "big_value", "monitor": "cpu_freq", "x": 5, "y": 270, "width": 155, "height": 45, "font_size": 16, "bg": "#0f172a"}, {"type": "big_value", "monitor": "gpu_freq", "x": 165, "y": 270, "width": 155, "height": 45, "font_size": 16, "bg": "#0f172a"}, {"type": "big_value", "monitor": "load_avg", "x": 325, "y": 270, "width": 150, "height": 45, "font_size": 16, "bg": "#0f172a"}]}