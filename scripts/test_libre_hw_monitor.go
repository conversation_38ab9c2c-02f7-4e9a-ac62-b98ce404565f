package main

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"time"
)

// LibreHardwareMonitorNode represents a node in the Libre Hardware Monitor data structure
type LibreHardwareMonitorNode struct {
	ID       int                         `json:"id"`
	Text     string                      `json:"Text"`
	Min      string                      `json:"Min"`
	Value    string                      `json:"Value"`
	Max      string                      `json:"Max"`
	SensorID string                      `json:"SensorId"`
	Type     string                      `json:"Type"`
	Children []LibreHardwareMonitorNode  `json:"Children"`
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run test_libre_hw_monitor.go <URL>")
		fmt.Println("Example: go run test_libre_hw_monitor.go http://192.168.11.19:8085")
		os.Exit(1)
	}

	url := os.Args[1] + "/data.json"
	fmt.Printf("Testing connection to Libre Hardware Monitor: %s\n", url)

	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	resp, err := client.Get(url)
	if err != nil {
		fmt.Printf("❌ Failed to connect: %v\n", err)
		os.Exit(1)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		fmt.Printf("❌ HTTP error: %d\n", resp.StatusCode)
		os.Exit(1)
	}

	fmt.Printf("✅ Connected successfully (HTTP %d)\n", resp.StatusCode)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("❌ Failed to read response: %v\n", err)
		os.Exit(1)
	}

	var root LibreHardwareMonitorNode
	if err := json.Unmarshal(body, &root); err != nil {
		fmt.Printf("❌ Failed to parse JSON: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("✅ JSON parsed successfully\n")
	fmt.Printf("📊 Data structure analysis:\n")

	// Analyze the data structure
	stats := analyzeNode(&root, 0)
	
	fmt.Printf("   - Total nodes: %d\n", stats.totalNodes)
	fmt.Printf("   - Sensor nodes: %d\n", stats.sensorNodes)
	fmt.Printf("   - CPU sensors: %d\n", stats.cpuSensors)
	fmt.Printf("   - GPU sensors: %d\n", stats.gpuSensors)
	fmt.Printf("   - Memory sensors: %d\n", stats.memorySensors)
	fmt.Printf("   - Fan sensors: %d\n", stats.fanSensors)
	fmt.Printf("   - Network sensors: %d\n", stats.networkSensors)

	fmt.Printf("\n🔍 Key sensor values found:\n")
	findKeyValues(&root, "")
}

type Stats struct {
	totalNodes     int
	sensorNodes    int
	cpuSensors     int
	gpuSensors     int
	memorySensors  int
	fanSensors     int
	networkSensors int
}

func analyzeNode(node *LibreHardwareMonitorNode, depth int) Stats {
	stats := Stats{totalNodes: 1}

	if node.Type != "" && node.Value != "" {
		stats.sensorNodes = 1
		
		sensorID := node.SensorID
		switch {
		case contains(sensorID, "/intelcpu/") || contains(sensorID, "/amdcpu/"):
			stats.cpuSensors = 1
		case contains(sensorID, "/gpu-nvidia/") || contains(sensorID, "/gpu-amd/"):
			stats.gpuSensors = 1
		case contains(sensorID, "/ram/"):
			stats.memorySensors = 1
		case node.Type == "Fan":
			stats.fanSensors = 1
		case contains(sensorID, "/nic/"):
			stats.networkSensors = 1
		}
	}

	for i := range node.Children {
		childStats := analyzeNode(&node.Children[i], depth+1)
		stats.totalNodes += childStats.totalNodes
		stats.sensorNodes += childStats.sensorNodes
		stats.cpuSensors += childStats.cpuSensors
		stats.gpuSensors += childStats.gpuSensors
		stats.memorySensors += childStats.memorySensors
		stats.fanSensors += childStats.fanSensors
		stats.networkSensors += childStats.networkSensors
	}

	return stats
}

func findKeyValues(node *LibreHardwareMonitorNode, path string) {
	currentPath := path
	if node.Text != "" {
		if currentPath != "" {
			currentPath += " > "
		}
		currentPath += node.Text
	}

	if node.Type != "" && node.Value != "" {
		switch node.Type {
		case "Load":
			if contains(node.Text, "CPU Total") || contains(node.Text, "GPU Core") || contains(node.Text, "Memory") {
				fmt.Printf("   📈 %s: %s (%s)\n", currentPath, node.Value, node.Type)
			}
		case "Temperature":
			if contains(node.Text, "CPU") || contains(node.Text, "GPU") {
				fmt.Printf("   🌡️  %s: %s (%s)\n", currentPath, node.Value, node.Type)
			}
		case "Clock":
			if contains(node.Text, "CPU Core") || contains(node.Text, "GPU Core") {
				fmt.Printf("   ⚡ %s: %s (%s)\n", currentPath, node.Value, node.Type)
			}
		case "Fan":
			fmt.Printf("   🌀 %s: %s (%s)\n", currentPath, node.Value, node.Type)
		case "Data":
			if contains(node.Text, "Memory") {
				fmt.Printf("   💾 %s: %s (%s)\n", currentPath, node.Value, node.Type)
			}
		case "Throughput":
			if contains(node.Text, "Speed") {
				fmt.Printf("   🌐 %s: %s (%s)\n", currentPath, node.Value, node.Type)
			}
		}
	}

	for i := range node.Children {
		findKeyValues(&node.Children[i], currentPath)
	}
}

func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || 
		(len(s) > len(substr) && 
			(s[:len(substr)] == substr || 
			 s[len(s)-len(substr):] == substr ||
			 findInString(s, substr))))
}

func findInString(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
